return {

    hero_data_iap = {
        hero_vesper             = { icon_idx=1,  starting_level=1, coming_soon=false },
        hero_raelyn             = { icon_idx=2,  starting_level=1, available_at_stage=3, coming_soon=false },
        hero_muyrn              = { icon_idx=3,  starting_level=3, available_at_stage=4, coming_soon=false },
        hero_venom              = { icon_idx=8,  starting_level=5, available_at_stage=9, coming_soon=false },
        hero_builder            = { icon_idx=4,  starting_level=5, iap=true, coming_soon=false },
        hero_robot              = { icon_idx=9,  starting_level=5, iap=true, coming_soon=false },
        hero_space_elf          = { icon_idx=5,  starting_level=5, iap=true, coming_soon=false },
        hero_lumenir            = { icon_idx=7,  starting_level=5, iap=true, coming_soon=false },
        hero_mecha              = { icon_idx=6,  starting_level=5, iap=true, coming_soon=false },
        hero_hunter             = { icon_idx=6,  starting_level=5, iap=true, coming_soon=false },
        hero_dragon_gem         = { icon_idx=6,  starting_level=5, iap=true, coming_soon=false },
        hero_bird               = { icon_idx=6,  starting_level=5, iap=true, coming_soon=false },
        hero_dragon_bone        = { icon_idx=6,  starting_level=5, iap=true, coming_soon=false },
        hero_witch              = { icon_idx=6,  starting_level=5, iap=true, coming_soon=false },
        hero_dragon_arb         = { icon_idx=6,  starting_level=5, iap=true, coming_soon=false },
        hero_lava               = { icon_idx=7,  starting_level=5, iap=true, coming_soon=false },
        hero_spider             = { icon_idx=6,  starting_level=5, iap=true, coming_soon=false },
        hero_wukong             = { icon_idx=6,  starting_level=5, iap=true, coming_soon=false },
    },

    -- 'available_at_stage' is the same as 'available_AFTER_stage' + 1
    -- in the case of crocs, you unlock the cards AFTER completing stage 20
    -- so the available_at_stage = 20 + 1 = 21
    hero_data_free = {
        hero_vesper             = { icon_idx=1,  starting_level=1,  coming_soon=false },
        hero_raelyn             = { icon_idx=2,  starting_level=1, available_at_stage=3,  coming_soon=false },
        hero_muyrn              = { icon_idx=3,  starting_level=3, available_at_stage=4,  coming_soon=false },
        hero_venom              = { icon_idx=8,  starting_level=5, available_at_stage=9,  coming_soon=false },
        hero_builder            = { icon_idx=4,  starting_level=5, available_at_stage=6,  coming_soon=false },
        hero_robot              = { icon_idx=9,  starting_level=5, available_at_stage=15,  coming_soon=false },
        hero_space_elf          = { icon_idx=5,  starting_level=5, available_at_stage=12,  coming_soon=false },
        hero_mecha              = { icon_idx=6,  starting_level=5, available_at_stage=13,  coming_soon=false },
        hero_lumenir            = { icon_idx=7,  starting_level=5, available_at_stage=17,  coming_soon=false },
        hero_hunter             = { icon_idx=7,  starting_level=5, available_at_stage=8,  coming_soon=false },
        hero_dragon_gem         = { icon_idx=7,  starting_level=5, available_at_stage=17,  coming_soon=false },
        hero_bird               = { icon_idx=7,  starting_level=5, available_at_stage=10,  coming_soon=false },
        hero_dragon_bone        = { icon_idx=7,  starting_level=5, available_at_stage=19,  coming_soon=false },
        hero_witch              = { icon_idx=7,  starting_level=5, available_at_stage=18, coming_soon=false },
        hero_dragon_arb         = { icon_idx=7,  starting_level=5, available_at_stage=21, coming_soon=false },
        hero_lava               = { icon_idx=7,  starting_level=5, iap=true, available_at_stage=25 , coming_soon=false },
        hero_spider             = { icon_idx=7,  starting_level=5, available_at_stage=29, coming_soon=false },
        hero_wukong             = { icon_idx=7,  starting_level=5, iap=true, available_at_stage=33, coming_soon=false },
    },

    hero_order_iap = {
        "hero_vesper",
        "hero_raelyn",
        "hero_muyrn",
        "hero_venom",
        "hero_builder",
        "hero_space_elf",
        "hero_hunter",
        "hero_witch",
        "hero_lava",
        "hero_wukong",
        "hero_robot",
        "hero_spider",
        "hero_mecha",
        "hero_bird",
        "hero_lumenir",
        "hero_dragon_gem",
        "hero_dragon_arb",
        "hero_dragon_bone",
    },
    
    hero_order_free = {
        "hero_vesper",
        "hero_raelyn",
        "hero_muyrn",
        "hero_builder",
        "hero_hunter",
        "hero_venom",
        "hero_bird",
        "hero_space_elf",
        "hero_mecha",
        "hero_robot",
        "hero_lumenir",
        "hero_dragon_gem",
        "hero_witch",
        "hero_lava",
        "hero_dragon_bone",
        "hero_dragon_arb",
        "hero_spider",
        "hero_wukong",
    },

    hero_order_censored_cn = {
        "hero_vesper",
        "hero_raelyn",
        "hero_muyrn",
        "hero_builder",
        "hero_hunter",
        "hero_venom",
        "hero_bird",
        "hero_space_elf",
        "hero_mecha",
        "hero_robot",
        "hero_lumenir",
        "hero_dragon_gem",
        "hero_witch",
    },

    tower_data_iap = {
        paladin_covenant        = {icon_idx=1},
        royal_archers           = {icon_idx=2},
        arcane_wizard           = {icon_idx=1},
        tricannon               = {icon_idx=1, available_at_stage=2},
        arborean_emissary       = {icon_idx=1, available_at_stage=6},
        demon_pit               = {icon_idx=1, available_at_stage=7},
        ballista                = {icon_idx=1, available_at_stage=5},
        flamespitter            = {icon_idx=1, available_at_stage=10},
        rocket_gunners          = {icon_idx=1, available_at_stage=12},
        ray                     = {icon_idx=1, available_at_stage=14},
        barrel                  = {icon_idx=1, iap=true},
        sand                    = {icon_idx=1, iap=true},
        elven_stargazers        = {icon_idx=1, iap=true},
        hermit_toad             = {icon_idx=1, iap=true},
        necromancer             = {icon_idx=1, iap=true},
        ghost                   = {icon_idx=1, iap=true},
        dark_elf                = {icon_idx=1, iap=true},
        dwarf                   = {icon_idx=1, iap=true},
        sparking_geode          = {icon_idx=1, iap=true},
        pandas                  = {icon_idx=1, iap=true},
    },

    tower_data_free = {
        paladin_covenant        = {icon_idx=1},
        royal_archers           = {icon_idx=2},
        arcane_wizard           = {icon_idx=1},
        tricannon               = {icon_idx=1, available_at_stage=2},
        arborean_emissary       = {icon_idx=1, available_at_stage=6},
        demon_pit               = {icon_idx=1, available_at_stage=7},
        ballista                = {icon_idx=1, available_at_stage=5},
        flamespitter            = {icon_idx=1, available_at_stage=10},
        rocket_gunners          = {icon_idx=1, available_at_stage=12},
        ray                     = {icon_idx=1, available_at_stage=14},
        barrel                  = {icon_idx=1, available_at_stage=7}, -- original 15, 13 to show on devices
        sand                    = {icon_idx=1, available_at_stage=11}, -- original 15, 13 to show on devices
        elven_stargazers        = {icon_idx=1, available_at_stage=9},
        necromancer             = {icon_idx=1, available_at_stage=8}, -- original 15, 13 to show on devices
        ghost                   = {icon_idx=1, available_at_stage=16},
        dark_elf                = {icon_idx=1, available_at_stage=19, censored_cn = true},
        hermit_toad             = {icon_idx=1, available_at_stage=21},
        dwarf                   = {icon_idx=1, available_at_stage=24,iap=true},
        sparking_geode          = {icon_idx=1, available_at_stage=29},
        pandas                  = {icon_idx=1, available_at_stage=32, iap=true},
    },

    tower_order_iap = {
        'royal_archers',
        'paladin_covenant',
        'arcane_wizard',
        'tricannon',
        'ballista',
        'arborean_emissary',
        'demon_pit',
        'flamespitter',
        'rocket_gunners',
        'ray',
        'barrel',
        'sand',
        'dark_elf',
        'dwarf',
        'elven_stargazers',
        'hermit_toad',
        'sparking_geode',
        'necromancer',
        'ghost',
        'pandas',
    },

    tower_order_free = {
        'royal_archers',
        'paladin_covenant',
        'arcane_wizard',
        'tricannon',
        'ballista',
        'arborean_emissary',
        'demon_pit',
        'barrel',
        'necromancer',
        'elven_stargazers',
        'flamespitter',
        'sand',
        'rocket_gunners',
        'ray',
        'ghost',
        'dark_elf',
        'hermit_toad',
        'dwarf',
        'sparking_geode',
        'pandas',
    },

    tower_order_censored_cn = {
        'royal_archers',
        'paladin_covenant',
        'arcane_wizard',
        'tricannon',
        'ballista',
        'arborean_emissary',
        'demon_pit',
        'barrel',
        'elven_stargazers',
        'flamespitter',
        'sand',
        'rocket_gunners',
        'ray',
        'ghost',
        'dark_elf',
    },

    item_order = {
        'cluster_bomb',
        'portable_coil',
        'scroll_of_spaceshift',
        'second_breath',
        'deaths_touch',
        'medical_kit',
        'winter_age',
        'loot_box',
        'summon_blackburn',
        'veznan_wrath',
    },

    item_order_censored_cn = {
        'cluster_bomb',
        'portable_coil',
        'scroll_of_spaceshift',
        'second_breath',
        'medical_kit',
        'winter_age',
        'loot_box',
        'summon_blackburn',
        'veznan_wrath',
    },
}

