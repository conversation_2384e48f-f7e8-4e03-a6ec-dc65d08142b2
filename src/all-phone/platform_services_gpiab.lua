--
-- Platform Services / Google Play In-app billing
--

local log = require('klua.log'):new('platform_services_gpiab')
require 'klua.table'
local signal = require 'hump.signal'
local storage = require 'storage'
local jnia = require 'all.jni_android'
local PSU = require 'platform_services_utils'
local RC = require 'remote_config'

------------------------------------------------------------
local iab = {}
iab.can_be_paused = true
iab.update_interval = 3  -- seconds (throttles update)
iab.late_update_delay = 5 -- seconds after last sync_products the late_update will be executed

iab.SRV_ID = 2           -- from KRGameActivity.java
iab.SRV_DISPLAY_NAME = 'Google Play'  -- show to player
iab.rc_suffix = 'gpiab' -- used to pull keys from remote_config

iab.async_purchases = true -- synchronizing purchases is an async call

--
iab.sku_index = {}  -- reverse sku index
iab.purchase_history_cache = {}
iab.purchases_cache = {}
iab.products_cache = {}
iab.sync_times = {}
iab.last_cached_purchases = nil
iab.sync_purchases_in_progress = nil
iab.purchase_in_progress = nil


-- from KRRequest
local REQ_ERR_IAP_PURCHASE_PENDING = 22;

------------------------------------------------------------
-- signal handlers
iab.signal_handlers = {
}

------------------------------------------------------------

function iab:init(name,params)
    if self.inited then
        log.debug('service %s already inited', name)
        goto skip_init
    end

    -- check there are products defined in remote_config
    if not RC.v['products_' .. self.rc_suffix] then
        log.error('products_%s not defined in remote_config',self.rc_suffix)
        return nil
    end

    -- custom params
    if not params or not params.pubkey then
        log.error('platform_services_gpiab requires pubkey param')
        return nil
    end
    self.pubkey = params.pubkey

    -- set pubkey
    jnia.set_service_param('gpiab_pubkey',self.pubkey)

    -- init
    do
        local result = jnia.init_service(self.SRV_ID)
        if result ~= 1 then
            log.error('platform_services_gpiab java init failed')
            return nil
        end
    end

    -- update reverse sku index
    self:update_sku_index()

    -- pending requests queue
    self.prq = PSU:new_prq()

    -- register signals
    for sn,fn in pairs(self.signal_handlers) do
        signal.register(sn, fn)
    end

    self.inited = true
    ::skip_init::

    -- add service names
    if not self.names then self.names = {} end
    if not table.contains(self.names,name) then
        table.insert(self.names,name)
    end

   return true
end

function iab:shutdown(name)
    if self.inited then
        -- remove signals
        for sn,fn in pairs(self.signal_handlers) do
            signal.remove(sn, fn)
        end

        -- TODO: destroy
    end
    self.names = nil
    self.inited = nil
end

------------------------------------------------------------
-- helper functions
function iab:update_sku_index()
    for _,n in pairs(RC.v['products_' .. self.rc_suffix]) do
        local p = self:get_product(n)
        local sku = (p and p.skus) and (p.skus[self.rc_suffix] or p.skus.default)
        if sku then
            self.sku_index[sku] = n
        end
    end
end

function iab:parse_purchases(str)
    if not str or str == '' then
        return {}
    end
    local lines = string.split(str,'\n')
    if not lines or #lines == 0 then
        return {}
    end
    local out = {}
    for _,line in pairs(lines) do
        local sku,token,signature,ackd,order_id,receipt = unpack(string.split(line,';'))
        local id = self.sku_index[sku]
        if not id then
            log.debug('sku:%s not found in sku_index', sku)
        else
            local t = {sku=sku,token=token,signature=signature,ackd=(ackd=='true'),order_id=order_id,receipt=receipt}
            t.id = id
            table.insert(out,t)
        end
    end
    return out
end

function iab:parse_products(str)
    if not str or str == '' then
        return {}
    end
    local lines = string.split(str,'\n')
    if not lines or #lines == 0 then
        return {}
    end
    local out = {}
    for _,line in pairs(lines) do
        local sku,title,description,price,price_micros,price_currency_code = unpack(string.split(line,';'))
        local id = self.sku_index[sku]
        if not id then
            log.debug('sku:%s not found in sku_index', sku)
        else
            local t = {sku=sku,title=title,description=description,price=price,price_micros=tonumber(price_micros),price_currency_code=price_currency_code}
            t.id = id
            table.insert(out,t)
        end
    end
    return out
end

function iab:deliver_purchase(id)
    log.info('delivering purchase for id: %s',id)
    local p = self:get_product(id,true)
    if not p then
        log.error('id:%s not found in remote_config' , id)
        return false
    end

    if not self.purchases_cache[id] then
        self.purchases_cache[id] = {}
    end
    local cp = self.purchases_cache[id]

    if p.includes then
        -- it's a pack of items
        for _,subid in pairs(p.includes) do
            log.debug('  delivering product pack:%s item:%s', id, subid)
            self:deliver_purchase(subid)
        end
        -- and then mark the pack itself
        cp.owned = true
    elseif p.gems then
        -- gems (consumable)
        local slot = storage:load_slot()
        if slot then
            slot.gems = slot.gems + p.reward
            -- gems that come from purchases (used for marketing)
            if not slot.gems_purchased then slot.gems_purchased = 0 end
            slot.gems_purchased = slot.gems_purchased + p.reward
            storage:save_slot(slot,nil,true) -- cloud sync
        end
    elseif p.includes_consumables then
        local slot = storage:load_slot()
        if slot then
            --items
            for _, v in pairs(p.includes_consumables) do
                if string.find(v.name, "item_") then
                    local item_id = string.gsub(v.name, 'item_', '')
                    if slot.items.status[item_id] and v.count then
                        slot.items.status[item_id] = slot.items.status[item_id] + v.count
                    else
                        log.error('id:%s item not found in slot' , v.item)
                    end
                    
                elseif string.find(v.name, "gems_") then
                    local g=self:get_product(v.name,true)
                    if g and g.gems then
                        slot.gems = slot.gems + g.reward
                        if not slot.gems_purchased then slot.gems_purchased = 0 end
                        slot.gems_purchased = slot.gems_purchased + g.reward
                    else
                        log.error('id:%s gempack not found in remote_config' , v.name)
                    end
                end          
            end 
            storage:save_slot(slot,nil,true)
        end
    else
        -- other product
        cp.owned = true
    end
    return true
end

function iab:get_container_dlc(id)
    local dlcs =  self:get_dlcs()
    for _, v in pairs(dlcs) do
        local p = self:get_product(v)
        if p and p.includes and table.contains(p.includes,id) then
            return p
        end
    end
end

------------------------------------------------------------
-- public interface

-- status
function iab:get_status()
    local result = jnia.get_service_status(self.SRV_ID)
    log.paranoid('get_status jni result: %s', result)
    if result == 1 then
        -- ready
        return true
    else
        -- not signed in, or other failure
        return nil
    end
end

function iab:is_premium()
    return self.premium
end

function iab:is_premium_valid()
    -- async purchases means that the premium status will not be known until the purchases are synchronized    
    return self.async_purchases and self.sync_times.purchases ~= nil
end

function iab:get_sync_status()
    return self.sync_times
end

-- requests
function iab:get_pending_requests()
    return self.prq
end

function iab:get_request_status(rid)
    local result = jnia.get_request_status(rid)
    log.paranoid('get_request_status (%s) jni result: %s', rid, result)
    return result
end

function iab:cancel_request(rid)
    if not rid then
        return
    end
    self.prq:remove(rid)
    jnia.delete_request(rid)
end

-- purchases
function iab:restore_purchases()
    self:sync_purchases()
end

function iab:sync_purchases(silent)

    local function cb_consume(status,req)
        if not self.prq:contains(req.id) then
            -- ignore if the request was already canceled
            return
        end
        iab:deliver_purchase(req.product_id)
        local data = {}
        data.order_id = req.order_id
        data.signature = req.signature
        data.token = req.token
        data.receipt = req.receipt
        signal.emit(SGN_PS_PURCHASE_PRODUCT_FINISHED, 'iap', (status==0), req.product_id, '', nil, data)
    end
        
    local function cb_sync_purchases(status, req)
        if not self.prq:contains(req.id) then
            -- ignore if the request was already canceled
            self.sync_purchases_in_progress = nil
            return
        end
        log.info('sync_purchases complete for req.id:%s status:%s', req.id,status)
        if status == 0 then             
            local purchases_string = jnia.get_cached_purchases(self.SRV_ID)
            iab.last_cached_purchases = purchases_string
            log.debug('purchases_string: %s',purchases_string)
            local purchases = self:parse_purchases(purchases_string)

            -- are we in premium mode?
            local was_premium = iab.premium
            iab.premium = nil
            for _,pu in pairs(purchases) do
                local p = self:get_product(pu.id,true)
                if p and p.premium then
                    if not was_premium then
                        -- emit the first time only
                        signal.emit(SGN_PS_PREMIUM_UNLOCKED, 'iap', 'google_pass')
                    end
                    iab.premium = true
                end
            end

            if not iab.premium then
                -- deliver purchases normally
                for _,pu in pairs(purchases) do
                    local p = self:get_product(pu.id,true)
                    if not p then
                        log.error('Sync purchases error. Product with id:%s sku:%s not found in remote_config', pu.id, pu.sku)
                    else
                        if p.consumable and pu.token then
                            if silent then
                                log.debug('silent update. ignoring consumable %s in this pass', pu.id)
                            else
                                -- if consumables in purchases list, consume now and update through callback
                                local rid = jnia.create_request_consume_product(self.SRV_ID,pu.token)
                                local req = self.prq:add(rid,'consume',cb_consume)
                                req.product_id = pu.id
                                req.sku = pu.sku
                                req.order_id = pu.order_id
                                req.signature = pu.signature
                                req.token = pu.token
                                req.receipt = pu.receipt
                                log.error('consumable %s found in sync_purchases. consuming now', pu.id)
                            end
                        elseif not p.consumable and not pu.ackd then
                            if silent then
                                log.debug('silent update. ignoring non-acknowledged %s in this pass', pu.id)
                            else
                                -- if unacknowledged non-consumable in list, consume now and update through callback
                                local rid = jnia.create_request_acknowledge_product(self.SRV_ID,pu.token)
                                local req = self.prq:add(rid,'ack',cb_consume)
                                req.product_id = pu.id
                                req.sku = pu.sku
                                req.order_id = pu.order_id
                                req.signature = pu.signature
                                req.token = pu.token
                                req.receipt = pu.receipt
                                log.error('unacknowledged %s found in sync_purchases. acknowledging now', pu.id)
                            end
                        else
                            log.debug('product owned:%s', pu.id)
                            self:deliver_purchase(pu.id)
                        end
                    end
                end
                -- store purchased heroes/towers/dlcs overwriting to remove refunds
                local ph = {}
                local pt = {}
                local pd = {}
                for id,p in pairs(self.purchases_cache) do
                    if string.starts(id,'hero_') then
                        table.insert(ph,id)
                    elseif string.starts(id,'tower_') then
                        table.insert(pt,id)
                    elseif string.starts(id,'dlc_') then
                        table.insert(pd,id)
                    end
                end
                local global = storage:load_global()
                global.purchased_heroes = ph
                global.purchased_towers = pt
                global.purchased_dlcs = pd
                storage:save_global(global)
            end

            self.sync_times.purchases = os.time()
        end
        signal.emit(SGN_PS_SYNC_PURCHASES_FINISHED, 'iap', (status==0))
        self.sync_purchases_in_progress = nil
    end
        
    --- sync purchases
    iab.sync_purchases_in_progress = nil
    local rid = jnia.create_request_query_purchases(self.SRV_ID)
    if rid < 0 then
        log.error('error syncing purchases')
        return nil
    else
        local req = self.prq:add(rid, 'sync_purchases', cb_sync_purchases)
        iab.sync_purchases_in_progress = true
        return rid
    end    
end

function iab:sync_purchase_history()
    local function cb_sync_purchase_history(status,req)
            if not self.prq:contains(req.id) then
            -- ignore if the request was already canceled
            return
        end
        if status == 0 then
            local history_str = jnia.get_cached_purchase_history(self.SRV_ID)
            log.debug('purchase_history string: %s',history_str)
            self.purchase_history_cache = self:parse_purchases(history_str)
            self.sync_times.purchase_history = os.time()
        end
        signal.emit(SGN_PS_SYNC_PURCHASE_HISTORY_FINISHED, 'iap', (status==0), self.purchase_history_cache)
    end

    log.info('sync purchase history')
    local rid = jnia.create_request_query_purchase_history(self.SRV_ID)
    if rid < 0 then
        log.error('error syncing purchase history')
        return nil
    else
        local req = self.prq:add(rid, 'sync_purchase_history', cb_sync_purchase_history)
        return rid
    end

end

function iab:purchase_product(id)
    local function cb_consume(status,req)
        if not self.prq:contains(req.id) then
            -- ignore if the request was already canceled
            return
        end
        if status == 0 then
            iab:deliver_purchase(req.product_id)
        end
        local data = {}
        data.order_id = req.order_id
        data.signature = req.signature
        data.token = req.token
        data.receipt = req.receipt
        signal.emit(SGN_PS_PURCHASE_PRODUCT_FINISHED, 'iap', (status==0), req.product_id, '', nil, data)
    end
    local function cb_purchase(status,req)
        if not self.prq:contains(req.id) then
            -- ignore if the request was already canceled
            iab.purchase_in_progress = nil
            return
        end
        local last_purchase = jnia.get_cached_purchases(self.SRV_ID)
        iab.last_cached_purchases = last_purchase
        log.debug('req:%s status:%s last_purchase:%s', req.id, status, last_purchase)
        local success = false
        local purchases = self:parse_purchases(last_purchase)
        if status == REQ_ERR_IAP_PURCHASE_PENDING then
            log.debug('purchase pending for product:%s', req.product_id)
            signal.emit(SGN_PS_PURCHASE_PRODUCT_PENDING, 'iap', success, req.product_id)
            iab.purchase_in_progress = nil
            return
            
        elseif status == 0 and purchases and purchases[1] then
            success = true
            local token = purchases[1].token
            local ackd = purchases[1].ackd
            log.debug('  cb_purchase: purchase.token=%s purchase.ackd=%s(%s)', token, ackd, type(ackd))
            if token then
                if req.consumable then
                    -- chain consume call
                    local crid = jnia.create_request_consume_product(self.SRV_ID,token)
                    local creq = self.prq:add(crid,'consume',cb_consume)
                    creq.product_id = req.product_id
                    creq.sku = req.sku
                    creq.order_id =  purchases[1].order_id
                    creq.signature = purchases[1].signature
                    creq.token = token
                    creq.receipt = purchases[1].receipt
                    log.debug('chaining consume product request id:%s for sku:%s token:%s',crid,req.product_id,token)
                    return  -- skip signal
                else
                    if ackd then
                        log.debug('non-consumable product already acknowledged. sku:%s token:%s', req.product_id,token)
                        -- fall through and deliver
                    else
                        -- chain acknowledge call
                        local crid = jnia.create_request_acknowledge_product(self.SRV_ID,token)
                        local creq = self.prq:add(crid,'ack',cb_consume)  -- same callback
                        creq.product_id = req.product_id
                        creq.sku = req.sku
                        creq.order_id =  purchases[1].order_id
                        creq.signature = purchases[1].signature
                        creq.token = token
                        creq.receipt = purchases[1].receipt
                        log.debug('chaining ack product request id:%s for sku:%s token:%s',crid,req.product_id,token)
                        return -- skip signal
                    end
                end
            end
            iab:deliver_purchase(req.product_id)
        end
        signal.emit(SGN_PS_PURCHASE_PRODUCT_FINISHED, 'iap', success, req.product_id)
        iab.purchase_in_progress = nil
    end

    --
    iab.purchase_in_progress = nil
    local p = self:get_product(id,true)
    if not p then
        log.error('could not initiate purchase of product %s. not found in remote_config', id)
        return nil
    end
    log.info('purchasing product:%s consume:%s',id,p.consumable)
    local sku = p.skus and (p.skus[self.rc_suffix] or p.skus.default)
    if not sku then
        log.error('missing sku for product: %s', id)
        return nil
    end
    local rid = jnia.create_request_purchase_product(self.SRV_ID,sku,p.consumable)
    if rid < 0 then
        log.error('error creating request to purchase iap %s consume:%s', id, p.consumable)
        return nil
    else
        local req = self.prq:add(rid,'purchase',cb_purchase)
        req.product_id = id
        req.sku = sku
        req.consumable = p.consumable
        iab.purchase_in_progress = true
        return rid
    end

end

-- products
function iab:get_product(id,reference)
    if not id then
        log.error('trying to get product with nil id')
        return nil
    end
    local k = 'product_' .. id
    local p = RC.v[k]
    if not p then
        log.error('product %s not found in remote_config %s',id,k)
        return nil
    end
    if reference then
        -- when no purchase or product data is required
        return p
    end
    -- return a copy of the product, with purchase and product info added
    local o = table.deepclone(p)
    if self.products_cache[id] then
        -- sku,title,description,price,price_micros,price_currency_code
        o = table.merge(o, self.products_cache[id])
    end
    if self.purchases_cache[id] then
        -- owned (except for consumables)
        o = table.merge(o, self.purchases_cache[id])
    end
    o.id = id
    return o
end

function iab:get_offers()
    if self:is_premium() then
        log.debug('gpiab is premium. no offers shown')
        return {}
    end
    local offers = RC.v['offers_'..self.rc_suffix]
    if not offers then
        log.error('offers_gpiab not found in remote_config')
        return {}
    end
    return offers
end

function iab:get_hero_sales()
    if self:is_premium() then
        log.debug('gpiab is premium. no hero sales shown')
        return {}
    end
    local offers = RC.v['hero_sales_'..self.rc_suffix]
    if not offers then
        log.error('hero_sales_gpiab not found in remote_config')
        return {}
    end
    return offers
end

function iab:get_tower_sales()
    if self:is_premium() then
        log.debug('gpiab is premium. no tower sales shown')
        return {}
    end
    local offers = RC.v['tower_sales_'..self.rc_suffix]
    if not offers then
        log.error('tower_sales_gpiab not found in remote_config')
        return {}
    end
    return offers
end

function iab:get_gems_sales()
    if self:is_premium() then
        log.error('gpiab is premium. no gems sales shown')
        return {}
    end
    local offers = RC.v['gems_sales_'..self.rc_suffix]
    if not offers then
        log.error('TEST IAP gems_sales_%s not found in remote_config',self.rc_suffix)
        return {}
    end
    return offers    
end

function iab:get_dlcs(owned)
    local dlcs = {}
    local premium = self:is_premium()
    for _,n in pairs(RC.v['products_' .. self.rc_suffix]) do
        if string.starts(n, 'dlc_') then
            if owned then
                local p = self:get_product(n)
                if p and (p.owned or premium) then
                    table.insert(dlcs, n)
                end
            else
                table.insert(dlcs, n)
            end
        end
    end
    return dlcs
end

function iab:get_formatted_currency(amount_micros, currency_code)
    log.debug('get_formatted_currency(%s,%s)', amount_micros, currency_code)
    return jnia.get_formatted_currency(amount_micros,currency_code)
end

function iab:sync_products()
    local function cb_sync_products(status,req)
        if not self.prq:contains(req.id) then
            -- ignore if the request was already canceled
            return
        end
        log.info('sync_products complete for req.id:%s status:%s', req.id,status)
        local success
        if status == 0 then
            -- success
            success = true
            local products_string = jnia.get_cached_products(self.SRV_ID)
            log.debug('products_string:%s',products_string)
            local store_products = self:parse_products(products_string)
            for _,sp in pairs(store_products) do
                local p = self:get_product(sp.id)
                if not p then
                    log.error('iap product %s not found in remote config', sp.id)
                else
                    if not self.products_cache[sp.id] then
                        self.products_cache[sp.id] = {}
                    end
                    local cp = self.products_cache[sp.id]
                    cp.sku = sp.sku
                    cp.title = sp.title
                    cp.description = sp.description
                    cp.price = sp.price
                    cp.price_micros = sp.price_micros  -- number
                    cp.price_currency_code = sp.price_currency_code
                    log.debug('iap cached product %s: %s',sp.id, getfulldump(p))
                end
            end
            self.sync_times.products = os.time()
        else
            -- error
            success = false
            self.sync_times.products = false
        end
        signal.emit(SGN_PS_SYNC_PRODUCTS_FINISHED, 'iap', success)
    end

    --
    self:update_sku_index()

    -- build list of skus for every product
    local skus_table = {}
    for _,n in pairs(RC.v['products_' .. self.rc_suffix]) do
        local p = self:get_product(n)
        if not p then
            log.error('product %s not defined in remote_config',n)
        elseif not p.skus or (not p.skus[self.rc_suffix] and not p.skus.default) then
            -- free item, do not query
        else
            table.insert(skus_table, p.skus[self.rc_suffix] or p.skus.default)
        end
    end
    local skus = table.concat(skus_table,',')
    -- query prducts
    local rid = jnia.create_request_sync_products(self.SRV_ID,skus)
    if rid < 0 then
        log.error('error creating request to sync products')
        return nil
    else
        self.prq:add(rid,'sync_products',cb_sync_products)
        return rid
    end
end

function iab:late_update(dt)
    -- if there were events that modified the purchases list, trigger a sync_purchases call, but only if it's not executing
    if not self.inited then
        return
    end
    if not self.sync_times.purchases or self.sync_times.purchases + self.late_update_delay > os.time() then
        -- no purchases synchronized yet, or in wait period
        return
    end
    if iab.sync_purchases_in_progress or iab.purchase_in_progress then
        log.debug('sync_purchases or purchase in progress. skipping...')
        return
    end
    local cached_purchases = jnia.get_cached_purchases(self.SRV_ID)
    if self.last_cached_purchases ~= cached_purchases then
        log.debug('cached purchases changed. triggering sync.')
        self:sync_purchases()
    end
    
end

------------------------------------------------------------
return iab

