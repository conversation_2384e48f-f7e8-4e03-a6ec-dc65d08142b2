--
--  Test Stub / Platform Services IAP
--

local log = require('klua.log'):new('test_platform_services_iap')
log.level = log.DEBUG_LEVEL
require 'klua.table'
local signal = require 'hump.signal'
local storage = require 'storage'
--local jnia = require 'all.jni_android'
local PSU = require 'platform_services_utils'
local RC = require 'remote_config'

------------------------------------------------------------
local tiap = {}
tiap.can_be_paused = true
tiap.update_interval = 1  -- seconds (throttles update)
tiap.rc_suffix = 'gpiab' -- used to pull keys from remote_config   (TEST USES GPIAB)

tiap.purchases_cache = {}
tiap.products_cache = {} 
tiap.sync_times = {}

-- TEST -----------
tiap._request_delay = 2
tiap._rid = 1
if KR_GAME == 'kr1' then
    tiap._purchases = {
        'hero_oni',
        'offer2',
        --'premium_unlock',
    }
    tiap._products = {
        {id='premium_unlock'   ,sku='com.ironhidegames.kingdomrush.googlepass'   , premium=true },
        {id='gem_pack_bag'     ,sku='com.armorgames.kingdomrushiphone.gempackbag'     ,title='',description='',price='$1.96',price_micros=1.96*1000000,price_currency_code='USD'},
        {id='gem_pack_barrel'  ,sku='com.armorgames.kingdomrushiphone.gempackbarrel'  ,title='',description='',price='$6.96',price_micros=6.96*1000000,price_currency_code='USD'},
        {id='gem_pack_chest'   ,sku='com.armorgames.kingdomrushiphone.gempackchest'   ,title='',description='',price='$7.96',price_micros=7.96*1000000,price_currency_code='USD'},
        {id='gem_pack_wagon'   ,sku='com.armorgames.kingdomrushiphone.gempackwagon'   ,title='',description='',price='$8.96',price_micros=8.96*1000000,price_currency_code='USD'},
        {id='gem_pack_vault'   ,sku='com.armorgames.kingdomrushiphone.gempackvault'   ,title='',description='',price='$9.96',price_micros=9.96*1000000,price_currency_code='USD'},
        {id='hero_bolin'       ,sku='com.armorgames.kingdomrushiphone.herobolin'      ,title='',description='',price='$3.86',price_micros=3.86*1000000,price_currency_code='USD'},
        {id='hero_magnus'      ,sku='com.armorgames.kingdomrushiphone.heromagnus'     ,title='',description='',price='$4.86',price_micros=4.86*1000000,price_currency_code='USD'},
        {id='hero_ignus'       ,sku='com.armorgames.kingdomrushiphone.heroignus'      ,title='',description='',price='$6.86',price_micros=6.86*1000000,price_currency_code='USD'},
        {id='hero_denas'       ,sku='com.armorgames.kingdomrushiphone.herodenas'      ,title='',description='',price='$7.86',price_micros=7.86*1000000,price_currency_code='USD'},
        {id='hero_elora'       ,sku='com.armorgames.kingdomrushiphone.herofrost'      ,title='',description='',price='$8.86',price_micros=8.86*1000000,price_currency_code='USD'},
        {id='hero_ingvar'      ,sku='com.armorgames.kingdomrushiphone.heroviking'     ,title='',description='',price='$9.86',price_micros=9.86*1000000,price_currency_code='USD'},
        {id='hero_hacksaw'     ,sku='com.armorgames.kingdomrushiphone.herorobot'      ,title='',description='',price='$1.86',price_micros=1.86*1000000,price_currency_code='USD'},
        {id='hero_oni'         ,sku='com.armorgames.kingdomrushiphone.herosamurai'    ,title='',description='',price='$2.86',price_micros=2.86*1000000,price_currency_code='USD'},
        {id='hero_thor'        ,sku='com.armorgames.kingdomrushiphone.herothor1'      ,title='',description='',price='$3.86',price_micros=3.86*1000000,price_currency_code='USD'},
        {id='offer1'           ,sku='com.ironhidegames.kingdomrush.offer.heropacks1'  ,title='',description='',price='$4.76',price_micros=4.76*1000000,price_currency_code='USD'},
        {id='offer2'           ,sku='com.ironhidegames.kingdomrush.offer.heropacks2'  ,title='',description='',price='$5.76',price_micros=5.76*1000000,price_currency_code='USD'},
        {id='offer3'           ,sku='com.ironhidegames.kingdomrush.offer.heropacks3'  ,title='',description='',price='$6.76',price_micros=6.76*1000000,price_currency_code='USD'},
        {id='offer4'           ,sku='com.ironhidegames.kingdomrush.offer.heropacks4'  ,title='',description='',price='$7.76',price_micros=7.76*1000000,price_currency_code='USD'},
        {id='offer5'           ,sku='com.ironhidegames.kingdomrush.offer.heropacks5'  ,title='',description='',price='$8.76',price_micros=8.76*1000000,price_currency_code='USD'},
        {id='offerall'         ,sku='com.ironhidegames.kingdomrush.offer.heropackall' ,title='',description='',price='$9.76',price_micros=9.76*1000000,price_currency_code='USD'},
    }
elseif KR_GAME == 'kr2' then 
    tiap._purchases = {
        'hero_pirate',
        'offer3',
        --'premium_unlock',
    }
    tiap._products = {
        {id='premium_unlock'  ,sku='com.ironhidegames.kingdomrush.frontiers.googlepass'   , premium=true },
        {id='gem_pack_bag'    ,sku='com.ironhidegames.frontiers.gempackbag',                     title='',description='',price='$0.79',price_micros=0.79*1000000,price_currency_code='USD'},
        {id='gem_pack_barrel' ,sku='com.ironhidegames.frontiers.gempackbarrel',                  title='',description='',price='$1.79',price_micros=1.79*1000000,price_currency_code='USD'},
        {id='gem_pack_chest'  ,sku='com.ironhidegames.frontiers.gempackchest',                   title='',description='',price='$2.79',price_micros=2.79*1000000,price_currency_code='USD'},
        {id='hero_dracolich'  ,sku='com.ironhidegames.frontiers.dracolich',                      title='',description='',price='$3.33',price_micros=3.33*1000000,price_currency_code='USD'},
        {id='hero_alien'      ,sku='com.ironhidegames.frontiers.heroalien',                      title='',description='',price='$3.33',price_micros=3.33*1000000,price_currency_code='USD'},
        {id='hero_dragon'     ,sku='com.ironhidegames.frontiers.herodragon',                     title='',description='',price='$3.33',price_micros=3.33*1000000,price_currency_code='USD'},
        {id='hero_giant'      ,sku='com.ironhidegames.frontiers.herogolem',                      title='',description='',price='$3.33',price_micros=3.33*1000000,price_currency_code='USD'},
        {id='hero_crab'       ,sku='com.ironhidegames.frontiers.herokarkinos',                   title='',description='',price='$3.33',price_micros=3.33*1000000,price_currency_code='USD'},
        {id='hero_minotaur'   ,sku='com.ironhidegames.frontiers.herominotaur2',                  title='',description='',price='$3.33',price_micros=3.33*1000000,price_currency_code='USD'},
        {id='hero_monk'       ,sku='com.ironhidegames.frontiers.heromonk',                       title='',description='',price='$3.33',price_micros=3.33*1000000,price_currency_code='USD'},
        {id='hero_monkey_god' ,sku='com.ironhidegames.frontiers.heromonkeygod',                  title='',description='',price='$3.33',price_micros=3.33*1000000,price_currency_code='USD'},
        {id='hero_pirate'     ,sku='com.ironhidegames.frontiers.heropirate',                     title='',description='',price='$3.33',price_micros=3.33*1000000,price_currency_code='USD'},
        {id='hero_priest'     ,sku='com.ironhidegames.frontiers.heropriest',                     title='',description='',price='$3.33',price_micros=3.33*1000000,price_currency_code='USD'},
        {id='hero_van_helsing',sku='com.ironhidegames.frontiers.herovanhelsing2',                title='',description='',price='$3.33',price_micros=3.33*1000000,price_currency_code='USD'},
        {id='hero_wizard'     ,sku='com.ironhidegames.frontiers.herowizard',                     title='',description='',price='$3.33',price_micros=3.33*1000000,price_currency_code='USD'},
        {id='offer1',         sku='com.ironhidegames.kingdomrush.frontiers.offer.heroalien',     title='',description='',price='$5.79',price_micros=5.79*1000000,price_currency_code='USD'},
        {id='offer2',         sku='com.ironhidegames.kingdomrush.frontiers.offer.heropack1',     title='',description='',price='$6.79',price_micros=6.79*1000000,price_currency_code='USD'},
        {id='offer3',         sku='com.ironhidegames.kingdomrush.frontiers.offer.heropack2',     title='',description='',price='$7.79',price_micros=7.79*1000000,price_currency_code='USD'},
        {id='offer7',         sku='com.ironhidegames.kingdomrush.frontiers.offer.heromonkeygod2',title='',description='',price='$8.79',price_micros=8.79*1000000,price_currency_code='USD'},
        {id='offerall',       sku='com.ironhidegames.kingdomrush.frontiers.offer.heropackall2',  title='',description='',price='$9.79',price_micros=9.79*1000000,price_currency_code='USD'},

        --{id='offertt',        sku='offerttsku', title='',description='', price='99.99', price_micros=99.99*1000000,price_currency_code='USD'},
    }
elseif KR_GAME == 'kr3' then
    tiap._purchases = {
        'hero_phoenix',
        'offer2',
        'gem_pack_barrel',
        --'premium_unlock',
    }
    tiap._products = {
        {id='premium_unlock'   ,sku='com.ironhidegames.kingdomrush.origins.googlepass'   , premium=true },
        {id='gem_pack_bag'     ,sku='com.ironhidegames.kingdomrush.origins.gempackbag'   , title='', description='', price='$0.39', price_micros=0.39*1000000, price_currency_code='USD' },
        {id='gem_pack_barrel'  ,sku='com.ironhidegames.kingdomrush.origins.gempackbarrel', title='', description='', price='$1.39', price_micros=1.39*1000000, price_currency_code='USD' },
        {id='gem_pack_chest'   ,sku='com.ironhidegames.kingdomrush.origins.gempackchest' , title='', description='', price='$2.39', price_micros=2.39*1000000, price_currency_code='USD' },
        {id='gem_pack_wagon'   ,sku='com.ironhidegames.kingdomrush.origins.gempackwagon' , title='', description='', price='$3.39', price_micros=3.39*1000000, price_currency_code='USD' },
        {id='gem_pack_vault'   ,sku='com.ironhidegames.kingdomrush.origins.gempackvault' , title='', description='', price='$4.39', price_micros=4.39*1000000, price_currency_code='USD' },
        {id='hero_regson'      ,sku="com.ironhidegames.kingdomrush.origins.heroregson"         , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },  
        {id='hero_xin'         ,sku="com.ironhidegames.kingdomrush.origins.heropanda"          , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='hero_veznan'      ,sku="com.ironhidegames.kingdomrush.origins.heroveznan"         , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='hero_elves_denas' ,sku="com.ironhidegames.kingdomrush.origins.herodenas"          , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='hero_bravebark'   ,sku="com.ironhidegames.kingdomrush.origins.herobravebark"      , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='hero_faustus'     ,sku="com.ironhidegames.kingdomrush.origins.herofaustus"        , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='hero_phoenix'     ,sku="com.ironhidegames.kingdomrush.origins.herophoenix"        , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='hero_durax'       ,sku="com.ironhidegames.kingdomrush.origins.herodurax"          , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='hero_lynn'        ,sku="com.ironhidegames.kingdomrush.origins.herolynn"           , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='hero_bruce'       ,sku="com.ironhidegames.kingdomrush.origins.herobruce"          , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='hero_wilbur'      ,sku="com.ironhidegames.kingdomrush.origins.herogyro"           , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='offer1'           ,sku="com.ironhidegames.kingdomrush.origins.offer.herofaustus"  , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='offer2'           ,sku="com.ironhidegames.kingdomrush.origins.offer.heropacks1"   , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='offer8'        ,sku="com.ironhidegames.kingdomrush.origins.offer.heropack7"    , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='offerall5'        ,sku="com.ironhidegames.kingdomrush.origins.offer.heropackall5" , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        --{id='offertt',        sku='offerttsku', title='',description='', price='99.99', price_micros=99.99*1000000,price_currency_code='USD'},
    }  
elseif KR_GAME == 'kr5' then  
    tiap._purchases = {
        'hero_dragon_gem',
    }
    tiap._products = {
         
        {id='hero_builder'         ,sku="com.ironhidegames.kingdomrush5.hero_torres"          , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='hero_space_elf '      ,sku="com.ironhidegames.kingdomrush5.hero_therien"         , title='', description='', price='$9.39', price_micros=20*1000000, price_currency_code='USD' }, 
        {id='hero_dragon_gem'      ,sku="com.ironhidegames.kingdomrush5.hero_kosmyr"         , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='sale_hero_space_elf'  ,sku="com.ironhidegames.kingdomrush5.sale_hero_therien"         , title='', description='', price='$5', price_micros=5*1000000, price_currency_code='USD' },  
        {id='sale_hero_builder'    ,sku="com.ironhidegames.kingdomrush5.sale_hero_torres"          , title='', description='', price='$5', price_micros=5*1000000, price_currency_code='USD' },
        {id='sale_hero_dragon_gem' ,sku="com.ironhidegames.kingdomrush5.sale_hero_kosmyr"         , title='', description='', price='$5', price_micros=5*1000000, price_currency_code='USD' },
        {id='hero_lumenir' ,sku="com.ironhidegames.kingdomrush5.hero_lumenir"          , title='', description='', price='$9.39', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='offer_dlc_1' ,sku="com.ironhidegames.kingdomrush5.dlc_dwarves"          , title='', description='', price='$7.99', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='offer_dlc_1_b' ,sku="com.ironhidegames.kingdomrush5.dlc_dwarves"          , title='', description='', price='$7.99', price_micros=9.39*1000000, price_currency_code='USD' },        
        {id='offer_dlc_2' ,sku="com.ironhidegames.kingdomrush5.dlc_wukong"          , title='', description='', price='$7.99', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='offer_allheroes_3' ,sku="com.ironhidegames.kingdomrush5.special_offer_all_heroes_3"          , title='', description='', price='$19.99', price_micros=9.39*1000000, price_currency_code='USD' },
        {id='gems_chest'         ,sku="com.ironhidegames.kingdomrush5.gems_chest"          , title='', description='', price='$19.99', price_micros=19.99*1000000, price_currency_code='USD' },
        {id='sale_gems_chest'         ,sku="com.ironhidegames.kingdomrush5.gems_chest_sale"          , title='', description='', price='$17.99', price_micros=17.99*1000000, price_currency_code='USD' },
    }
end

------------------------------------------------------------

------------------------------------------------------------
-- signal handlers
tiap.signal_handlers = {
}

------------------------------------------------------------

function tiap:init(name,params)
    log.error('TEST IAP / test_platform_services_iap loaded')

    if self.inited then
        log.debug('TEST IAP service %s already inited', name)
        goto skip_init
    end

    -- check there are products defined in remote_config
    if not RC.v['products_' .. self.rc_suffix] then
        log.error('products_%s not defined in remote_config',self.rc_suffix)        
        return nil
    end

    -- pending requests queue
    self.prq = PSU:new_prq()

    -- register signals
    for sn,fn in pairs(self.signal_handlers) do
        signal.register(sn, fn)
    end

    self.inited = true
    ::skip_init::

    -- add service names
    if not self.names then self.names = {} end
    if not table.contains(self.names,name) then
        table.insert(self.names,name)
    end
    
   return true        
end

function tiap:shutdown(name)
    if self.inited then
        -- remove signals
        for sn,fn in pairs(self.signal_handlers) do
            signal.remove(sn, fn)
        end

        -- TODO: destroy
    end
    self.names = nil
    self.inited = nil
end

------------------------------------------------------------
-- helper functions

function tiap:deliver_purchase(id)
    log.debug('TEST IAP delivering purchase for id: %s',id)
    local p = self:get_product(id,true)
    if not p then
        log.error('TEST IAP id:%s not found in remote_config' , id)
        return false
    end

    if not self.purchases_cache[id] then
        self.purchases_cache[id] = {}
    end
    local cp = self.purchases_cache[id]

    if p.includes then        
        -- it's a pack of items
        for _,subid in pairs(p.includes) do
            log.debug('TEST IAP   delivering product pack:%s item:%s', id, subid)
            self:deliver_purchase(subid)
        end
        -- and then mark the pack itself
        cp.owned = true
    elseif p.gems then
        -- gems (consumable)
        local slot = storage:load_slot()
        if slot then 
            slot.gems = slot.gems + p.reward
            -- gems that come from purchases (used for marketing)
            if not slot.gems_purchased then slot.gems_purchased = 0 end
            slot.gems_purchased = slot.gems_purchased + p.reward
            storage:save_slot(slot,nil,true) -- cloud sync
        end
    else
        -- other product
        cp.owned = true
    end
    return true
end

------------------------------------------------------------
-- public interface

-- status
function tiap:get_status()
    return true
end

function tiap:is_premium()
    return self.premium
end

function tiap:is_premium_valid()
    return true
end

-- requests
function tiap:get_pending_requests()
    return self.prq
end

function tiap:get_sync_status()
    return self.sync_times
end

function tiap:get_request_status(rid)
    local req = self.prq[rid]
    if not req then
        return -1
    elseif love.timer.getTime() - req.ts < self._request_delay then
        return 1
    else
        return 0
    end
end

function tiap:cancel_request(rid)
    self.prq:remove(rid)
end

-- purchases
function tiap:restore_purchases()
    -- sync purchases is synchronous in this version, so fake a signal
    self:sync_purchases()
    signal.emit(SGN_PS_RESTORE_PURCHASES_FINISHED, 'iap', (status==0))
end

function tiap:sync_purchases(silent)
    log.error('TEST IAP')
    -- are we in premium mode?    
    local was_premium = tiap.premium
    tiap.premium = nil
    for _,v in pairs(self._purchases) do
        local p = self:get_product(v,true)
        if p and p.premium then
            if not was_premium then
                -- emit the first time only
                signal.emit(SGN_PS_PREMIUM_UNLOCKED, 'iap', 'test_iap')
            end            
            tiap.premium = true
        end
    end
    if not tiap.premium then
        -- deliver purchases normally if not premium
        for _,v in pairs(self._purchases) do
            self:deliver_purchase(v)
        end
        -- store purchased heroes overwriting to remove refunds
        local ph = {}
        for id,p in pairs(self.purchases_cache) do
            if string.starts(id,'hero_') then 
                table.insert(ph,id)
            end
        end
        local global = storage:load_global()
        global.purchased_heroes = ph
        storage:save_global(global)        
    end
    self.sync_times.purchases = os.time()
end

function tiap:purchase_product(id)    
    local function cb_consume(status,req)
        tiap:deliver_purchase(req.product_id)
        signal.emit(SGN_PS_PURCHASE_PRODUCT_FINISHED, 'iap', (status==0), req.product_id)
    end    
    local function cb_purchase(status,req)                
        local success = true
        if req.consumable then 
            -- chain consume call                        
            local crid = self._rid
            self._rid = self._rid + 1
            local creq = self.prq:add(crid,'consume',cb_consume)
            creq.product_id = req.product_id
            creq.sku = req.sku
            log.debug('TEST IAP chaining consume product request id:%s for sku:%s token:%s',crid,req.product_id,token)  
            return  -- skip signal
        end
        tiap:deliver_purchase(req.product_id)
        signal.emit(SGN_PS_PURCHASE_PRODUCT_FINISHED, 'iap', success, req.product_id)        
    end

    --
    local p = self:get_product(id,true)
    if not p then
        log.error('TEST IAP could not initiate purchase of product %s. not found in remote_config', id)
        return nil
    end    
    log.debug('TEST IAP purchasing product:%s consume:%s',id,p.consumable)
    local sku = p.skus and ( p.skus[self.rc_suffix] or p.skus.default )
    if not sku then
        log.error('TEST IAP missing sku for product: %s', id)
        return nil
    end
    local rid = self._rid
    self._rid = self._rid + 1        
    local req = self.prq:add(rid,'purchase',cb_purchase)
    req.product_id = id
    req.sku = sku
    req.consumable = p.consumable
    return rid
end

-- products 
function tiap:get_product(id,reference)
    local k = 'product_' .. id
    local p = RC.v[k]
    if not p then
        log.error('TEST IAP product %s not found in remote_config %s',id,k)
        return nil
    end
    if reference then
        -- when no purchase or product data is required
        return p
    end
    local o = table.deepclone(p)
    if self.products_cache[id] then
        -- sku,title,description,price,price_micros,price_currency_code
        o = table.merge(o, self.products_cache[id])
    end
    if self.purchases_cache[id] then
        -- owned (except for consumables)
        o = table.merge(o, self.purchases_cache[id])
    end    
    o.id = id
    return o
end

function tiap:get_offers()
    if self:is_premium() then
        log.error('hero_sales_gpiab is premium. no hero sales shown')
        return {}
    end

    local offers = RC.v['offers_'..self.rc_suffix]
    if not offers then
        log.error('TEST IAP offers_%s not found in remote_config',self.rc_suffix)
        return {}
    end
    return offers
end

function tiap:get_hero_sales()
    if self:is_premium() then
        log.error('hero_sales_gpiab is premium. no hero sales shown')
        return {}
    end
    local offers = RC.v['hero_sales_'..self.rc_suffix]
    if not offers then
        log.error('TEST IAP hero_sales_%s not found in remote_config',self.rc_suffix)
        return {}
    end
    return offers    
end

function tiap:get_tower_sales()
    if self:is_premium() then
        log.error('tower_sales_gpiab is premium. no hero sales shown')
        return {}
    end
    local offers = RC.v['tower_sales_'..self.rc_suffix]
    if not offers then
        log.error('TEST IAP tower_sales_%s not found in remote_config',self.rc_suffix)
        return {}
    end
    return offers    
end

function tiap:get_gems_sales()
    if self:is_premium() then
        log.error('gems_sales_gpiab is premium. no gems sales shown')
        return {}
    end
    local offers = RC.v['gems_sales_'..self.rc_suffix]
    if not offers then
        log.error('TEST IAP gems_sales_%s not found in remote_config',self.rc_suffix)
        return {}
    end
    return offers    
end

function tiap:get_dlcs(owned)
    local dlcs = {}
    for _,n in pairs(RC.v['products_' .. self.rc_suffix]) do
        if string.starts(n, 'dlc_') then
            if owned then
                local p = self:get_product(n)
                if p and p.owned then
                    table.insert(dlcs, n)
                end
            else
                table.insert(dlcs, n)
            end
        end
    end
    return dlcs
end

function tiap:get_formatted_currency(amount_micros, currency_code)
    return string.format('$%.2f',amount_micros/1000000)
end

function tiap:sync_products()
    local function cb_sync_products(status,req)
        log.debug('TEST IAP sync_products complete for req.id:%s status:%s', req.id,status)
        local success = true
        for _,sp in pairs(self._products) do
            local p = self:get_product(sp.id,true)
            if not p then
                log.error('TEST IAP iap product %s not found in remote_config', sp.id)
            else
                if not self.products_cache[sp.id] then
                    self.products_cache[sp.id] = {}
                end
                local cp = self.products_cache[sp.id]
                cp.sku = sp.sku
                cp.title = sp.title
                cp.description = sp.description
                cp.price = sp.price
                cp.price_micros = sp.price_micros  -- number
                cp.price_currency_code = sp.price_currency_code
                log.debug('TEST IAP iap cached product %s: %s',sp.id, '')--getfulldump(p))
            end
        end
        signal.emit(SGN_PS_SYNC_PRODUCTS_FINISHED, 'iap', success)
    end
    --
    self.sync_times.products = os.time()
    local rid = self._rid
    self._rid = self._rid + 1        
    self.prq:add(rid,'sync_products',cb_sync_products)
    return rid
end

function tiap:get_container_dlc(id)
    local dlcs =  self:get_dlcs()
    for _, v in pairs(dlcs) do
        local p = self:get_product(v)
        if p and p.includes and table.contains(p.includes,id) then
            return p
        end
    end
end

------------------------------------------------------------
return tiap

