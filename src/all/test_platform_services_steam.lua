local log = require('klua.log'):new('platform_services_iap_premium')
log.level = log.DEBUG_LEVEL
require 'klua.table'
local signal = require 'hump.signal'
local storage = require 'storage'
local PSU = require 'platform_services_utils'
local RC = require 'remote_config'
local web_url =  'steam://openurl/https://store.steampowered.com/app/%s'

------------------------------------------------------------
local tiap = {}
tiap.can_be_paused = true
tiap.update_interval = 1  -- seconds (throttles update)
tiap.rc_suffix = 'iap_premium'

tiap.purchases_cache = {}
tiap.products_cache = {} 
tiap.sync_times = {}

tiap._request_delay = 2
tiap._rid = 1

------------------------------------------------------------
-- signal handlers
tiap.signal_handlers = {
}


function tiap:init(name,params)
    log.debug('platform_services_iap_premium loaded')

    if self.inited then
        log.debug('platform_services_iap_premium %s already inited', name)
        goto skip_init
    end

    -- pending requests queue
    self.prq = PSU:new_prq()

    -- register signals
    for sn,fn in pairs(self.signal_handlers) do
        signal.register(sn, fn)
    end

    self.app_id = params.app_id
    self.dlcs = params.dlcs
    self.owned_dlcs = {}  -- ADD HERE VIA REPL TO TEST

    self.inited = true
    ::skip_init::

    -- add service names
    if not self.names then self.names = {} end
    if not table.contains(self.names,name) then
        table.insert(self.names,name)
    end
    
   return true        
end

function tiap:shutdown(name)
    if self.inited then
        -- remove signals
        for sn,fn in pairs(self.signal_handlers) do
            signal.remove(sn, fn)
        end
    end
    self.names = nil
    self.inited = nil
end

------------------------------------------------------------
-- public interface

-- status
function tiap:get_status()
    return true
end

function tiap:is_premium()
    return true,{'dlcs'}
end

function tiap:is_premium_valid()
    return true
end

-- requests
function tiap:get_pending_requests()
    return self.prq
end

function tiap:get_sync_status()
    return self.sync_times
end

function tiap:get_request_status(rid)
    local req = self.prq[rid]
    if not req then
        return -1
    elseif love.timer.getTime() - req.ts < self._request_delay then
        return 1
    else
        return 0
    end
end

function tiap:cancel_request(rid)
    self.prq:remove(rid)
end

-- purchases
function tiap:sync_purchases(silent)
    self.sync_times.purchases = os.time()
end

function tiap:purchase_product(id)
    for _,dlc in pairs(self.dlcs) do
        if id == dlc.id then
            local web =string.format(web_url,dlc.app_id)
            love.system.openURL(web)
        end
    end
    return nil
end

-- products 
function tiap:get_product(id,reference)
    if not self.inited then
        log.warning('platform_services_iap_premium inited yet')
        return nil
    end
    if not self.dlcs then
        return nil
    end
    local owned_dlcs = self:get_dlcs(true)
    for _,dlc in pairs(self.dlcs) do
        local dlc_owned = table.contains(owned_dlcs, dlc.id)
        if id == dlc.id then
            local includes = dlc.includes and table.deepclone(dlc.includes)
            return {id=id, owned=dlc_owned, includes=includes}
        elseif dlc.includes then
            for _,iid in pairs(dlc.includes) do
                if id == iid then
                    return {id=iid, owned=dlc_owned}
                end
            end
        end
    end
    return nil
end

function tiap:get_offers()
    log.debug('platform_services_iap_premium does not show offers')
    return {}
end

function tiap:get_hero_sales()
    log.debug('platform_services_iap_premium does not show hero sales')
    return {}
end

function tiap:get_tower_sales()
    log.debug('platform_services_iap_premium does not show tower sales')
    return {}
end

function tiap:get_gems_sales()
    log.debug('platform_services_iap_premium does not show gems sales')
    return {}
end

function tiap:get_dlcs(owned)
    local list = {}
    local gs = require 'game_settings'
    if gs.dlc_names then
        for _,v in pairs(gs.dlc_names) do
            if not owned or table.contains(self.owned_dlcs, v.id) then
                table.insert(list, v.id)
            end
        end
    end
    return list
end

function tiap:get_container_dlc(id)
    local dlcs =  self:get_dlcs()
    for _, v in pairs(dlcs) do
        local p = self:get_product(v)
        if p and p.includes and table.contains(p.includes,id) then
            return p
        end
    end
    return nil
end

function tiap:get_formatted_currency(amount_micros, currency_code)
    return string.format('$%.2f',amount_micros/1000000)
end

function tiap:sync_products()
    return -1
end

------------------------------------------------------------
return tiap

